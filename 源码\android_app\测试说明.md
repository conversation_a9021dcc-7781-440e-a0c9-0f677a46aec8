# 📱 通讯录功能测试说明

## 🎯 目的
解决通讯录读取不到的问题，通过详细的调试和测试来定位问题所在。

## 🔧 已做的改进

### 1. 增强的日志输出
- 添加了详细的Log.d()语句跟踪整个流程
- 在关键步骤都有日志输出
- 可以通过adb logcat查看详细信息

### 2. 启动时自检
- 应用启动时自动检查权限声明
- 检查运行时权限状态
- 测试基本的数据库查询功能

### 3. 专门的测试页面
- 创建了test_contacts.html测试页面
- 包含多个测试按钮
- 实时显示测试结果

### 4. 多种测试方法
- **检查权限**：检查当前权限状态
- **读取通讯录**：完整的通讯录读取流程
- **直接测试**：简化的测试方法
- **获取APP信息**：显示应用和系统信息

## 🚀 测试步骤

### 第一步：编译安装
1. 在Android Studio中打开项目
2. 连接Android设备或启动模拟器
3. 点击Run按钮编译安装

### 第二步：查看启动日志
使用以下命令查看启动时的自检日志：
```bash
adb logcat -s MainActivity
```

### 第三步：使用测试页面
1. 应用启动后会自动显示测试页面
2. 依次点击各个测试按钮
3. 观察页面显示的结果
4. 同时查看logcat中的详细日志

### 第四步：权限测试
1. 点击"检查权限"查看当前状态
2. 点击"读取通讯录"触发权限请求
3. 在权限对话框中选择"允许"
4. 观察是否成功读取到联系人

## 🔍 问题排查

### 如果权限对话框不显示
可能原因：
- 权限已被永久拒绝
- 系统设置中禁用了权限请求

解决方法：
1. 进入设置 > 应用管理 > 代乱设计贷款 > 权限
2. 手动开启通讯录权限
3. 或者卸载重装应用

### 如果查询返回0条记录
可能原因：
- 通讯录中确实没有联系人
- 查询条件有问题
- 权限不足

解决方法：
1. 添加一些测试联系人
2. 检查日志中的详细错误信息
3. 确认权限已正确授权

### 如果JavaScript接口不可用
可能原因：
- WebView配置问题
- JavaScript未正确启用
- 接口注入失败

解决方法：
1. 检查页面是否显示"检测到APP环境"
2. 查看logcat中的WebView相关日志
3. 确认User-Agent包含"DaiLuanSheJApp"

## 📊 预期结果

### 正常情况下的日志输出：
```
D/MainActivity: MainActivity onCreate
D/MainActivity: === 通讯录功能检查开始 ===
D/MainActivity: AndroidManifest.xml中是否声明READ_CONTACTS权限: true
D/MainActivity: 运行时权限状态: false
D/MainActivity: Android版本: 11 (API 30)
D/MainActivity: === 通讯录功能检查结束 ===
D/MainActivity: 页面加载完成: file:///android_asset/test_contacts.html
```

### 成功读取通讯录后的日志：
```
D/MainActivity: JavaScript调用requestContacts()
D/MainActivity: 权限未授权，请求权限
D/MainActivity: 用户授权了通讯录权限
D/MainActivity: 开始读取通讯录
D/MainActivity: 数据库查询结果: 成功
D/MainActivity: 游标记录数: 25
D/MainActivity: 成功读取 25 个有效联系人
```

## 🛠️ 调试命令

### 查看应用权限状态
```bash
adb shell dumpsys package com.dailuanshej.loan | grep permission
```

### 手动授权权限
```bash
adb shell pm grant com.dailuanshej.loan android.permission.READ_CONTACTS
```

### 清除应用数据重新测试
```bash
adb shell pm clear com.dailuanshej.loan
```

## 📞 联系支持
如果按照以上步骤仍然无法解决问题，请提供：
1. 完整的logcat日志
2. 设备型号和Android版本
3. 测试页面显示的具体错误信息
4. 权限设置的截图

这样可以更好地定位和解决问题。
