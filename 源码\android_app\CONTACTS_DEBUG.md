# 🔍 通讯录功能调试指南

## 📱 问题描述
通讯录读取功能无法正常工作，需要进行详细的调试和测试。

## 🛠️ 已添加的调试功能

### 1. 详细日志输出
在 `MainActivity.java` 中添加了大量的 `Log.d()` 语句来跟踪：
- 权限检查过程
- 通讯录查询过程
- JavaScript接口调用
- 错误信息

### 2. 启动时自检
应用启动时会自动执行 `checkContactsFeature()` 方法，检查：
- AndroidManifest.xml中的权限声明
- 运行时权限状态
- Android系统版本
- 简单的通讯录查询测试

### 3. 测试页面
创建了专门的测试页面 `test_contacts.html`，包含：
- APP环境检测
- 权限状态检查
- 通讯录读取测试
- 详细的结果显示

## 🚀 测试步骤

### 1. 编译和安装
1. 在Android Studio中打开项目
2. 连接Android设备或启动模拟器
3. 点击 "Run" 按钮编译并安装应用

### 2. 查看日志
使用以下命令查看详细日志：
```bash
adb logcat -s MainActivity
```

或者在Android Studio的Logcat窗口中过滤 "MainActivity" 标签。

### 3. 测试流程
1. **启动应用**：应用会自动加载测试页面
2. **查看自检结果**：在日志中查看启动时的自检信息
3. **测试权限检查**：点击"检查权限"按钮
4. **测试通讯录读取**：点击"读取通讯录"按钮
5. **观察结果**：查看页面显示和日志输出

## 🔍 调试要点

### 1. 权限相关
- 检查 AndroidManifest.xml 中是否正确声明了 `READ_CONTACTS` 权限
- 确认运行时权限是否正确授权
- 查看权限请求对话框是否正常显示

### 2. 数据库查询
- 检查 ContentResolver 查询是否成功
- 确认 Cursor 是否为 null
- 查看查询返回的记录数量

### 3. JavaScript接口
- 确认 AndroidContacts 对象是否正确注入
- 检查 JavaScript 回调函数是否被正确调用
- 查看数据传递是否正常

### 4. 系统兼容性
- 确认Android版本是否支持
- 检查是否有特殊的权限限制
- 查看是否有厂商定制的限制

## 📋 常见问题排查

### 问题1：权限对话框不显示
**可能原因**：
- 权限已经被永久拒绝
- 系统设置中禁用了权限请求

**解决方案**：
- 在设置中手动授权
- 卸载重装应用

### 问题2：查询返回空结果
**可能原因**：
- 通讯录中没有联系人
- 查询条件有误
- 权限不足

**解决方案**：
- 添加一些测试联系人
- 检查查询语句
- 确认权限状态

### 问题3：JavaScript接口不可用
**可能原因**：
- WebView设置有误
- JavaScript未启用
- 接口注入失败

**解决方案**：
- 检查WebView配置
- 确认JavaScript已启用
- 重新注入接口

## 🔧 调试命令

### 查看应用权限
```bash
adb shell dumpsys package com.dailuanshej.loan | grep permission
```

### 查看通讯录数据
```bash
adb shell content query --uri content://com.android.contacts/data/phones
```

### 清除应用数据
```bash
adb shell pm clear com.dailuanshej.loan
```

### 手动授权权限
```bash
adb shell pm grant com.dailuanshej.loan android.permission.READ_CONTACTS
```

## 📊 预期日志输出

正常情况下，应该看到类似以下的日志：
```
D/MainActivity: MainActivity onCreate
D/MainActivity: === 通讯录功能检查开始 ===
D/MainActivity: AndroidManifest.xml中是否声明READ_CONTACTS权限: true
D/MainActivity: 运行时权限状态: false
D/MainActivity: Android版本: 11 (API 30)
D/MainActivity: 没有权限，跳过测试查询
D/MainActivity: === 通讯录功能检查结束 ===
D/MainActivity: 页面加载完成: file:///android_asset/test_contacts.html
D/MainActivity: JavaScript调用requestContacts()
D/MainActivity: 权限未授权，请求权限
D/MainActivity: 请求通讯录权限
D/MainActivity: 权限请求结果 - requestCode: 100, grantResults: 0
D/MainActivity: 用户授权了通讯录权限
D/MainActivity: 开始读取通讯录
D/MainActivity: 开始查询通讯录数据库
D/MainActivity: 数据库查询结果: 成功
D/MainActivity: 游标记录数: 25
D/MainActivity: 成功读取 25 个有效联系人
```

## 🎯 下一步
根据日志输出和测试结果，可以进一步定位问题并制定解决方案。
