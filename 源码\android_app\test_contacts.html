<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通讯录测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .contacts-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
        }
        .contact-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .contact-name {
            font-weight: bold;
            color: #333;
        }
        .contact-phone {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>通讯录功能测试</h1>
        
        <div id="app-detection" class="status info">
            检测APP环境中...
        </div>
        
        <div id="permission-status" class="status info">
            检查权限状态中...
        </div>
        
        <button class="button" onclick="checkPermission()">检查权限</button>
        <button class="button" onclick="requestContacts()">读取通讯录</button>
        <button class="button" onclick="getAppInfo()">获取APP信息</button>
        <button class="button" onclick="clearResults()">清空结果</button>
        
        <div id="results"></div>
        
        <div id="contacts-container" style="display: none;">
            <h3>联系人列表</h3>
            <div id="contacts-list" class="contacts-list"></div>
        </div>
    </div>

    <script>
        // 检测APP环境
        function detectAppEnvironment() {
            const isInApp = navigator.userAgent.includes('DaiLuanSheJApp') || 
                           (typeof AndroidContacts !== 'undefined');
            
            const detectionDiv = document.getElementById('app-detection');
            if (isInApp) {
                detectionDiv.className = 'status success';
                detectionDiv.textContent = '✅ 检测到APP环境';
                
                // 自动检查权限状态
                setTimeout(checkPermission, 500);
            } else {
                detectionDiv.className = 'status error';
                detectionDiv.textContent = '❌ 未检测到APP环境，请在APP中打开此页面';
            }
            
            return isInApp;
        }
        
        // 检查权限状态
        function checkPermission() {
            if (typeof AndroidContacts === 'undefined') {
                showResult('❌ AndroidContacts接口不可用', 'error');
                return;
            }
            
            try {
                const hasPermission = AndroidContacts.isContactsPermissionGranted();
                const statusDiv = document.getElementById('permission-status');
                
                if (hasPermission) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ 通讯录权限已授权';
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '❌ 通讯录权限未授权';
                }
                
                showResult(`权限检查结果: ${hasPermission ? '已授权' : '未授权'}`, hasPermission ? 'success' : 'error');
            } catch (error) {
                showResult('检查权限时出错: ' + error.message, 'error');
            }
        }
        
        // 请求读取通讯录
        function requestContacts() {
            if (typeof AndroidContacts === 'undefined') {
                showResult('❌ AndroidContacts接口不可用', 'error');
                return;
            }
            
            try {
                showResult('📱 正在请求读取通讯录...', 'info');
                AndroidContacts.requestContacts();
            } catch (error) {
                showResult('请求通讯录时出错: ' + error.message, 'error');
            }
        }
        
        // 获取APP信息
        function getAppInfo() {
            if (typeof AndroidContacts === 'undefined') {
                showResult('❌ AndroidContacts接口不可用', 'error');
                return;
            }
            
            try {
                const appInfo = AndroidContacts.getAppInfo();
                showResult('APP信息: ' + appInfo, 'info');
            } catch (error) {
                showResult('获取APP信息时出错: ' + error.message, 'error');
            }
        }
        
        // 显示结果
        function showResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const resultItem = document.createElement('div');
            resultItem.className = 'status ' + type;
            resultItem.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            resultsDiv.appendChild(resultItem);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // 清空结果
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('contacts-container').style.display = 'none';
        }
        
        // 通讯录读取成功回调
        window.onContactsRead = function(contacts) {
            console.log('收到联系人数据:', contacts);
            showResult(`✅ 成功读取 ${contacts.length} 个联系人`, 'success');
            
            // 显示联系人列表
            const contactsList = document.getElementById('contacts-list');
            const contactsContainer = document.getElementById('contacts-container');
            
            contactsList.innerHTML = '';
            
            if (contacts.length > 0) {
                contacts.forEach((contact, index) => {
                    const contactItem = document.createElement('div');
                    contactItem.className = 'contact-item';
                    contactItem.innerHTML = `
                        <div class="contact-name">${contact.name || '未知姓名'}</div>
                        <div class="contact-phone">${contact.phone || '未知号码'}</div>
                    `;
                    contactsList.appendChild(contactItem);
                });
                contactsContainer.style.display = 'block';
            } else {
                showResult('⚠️ 通讯录为空或没有有效联系人', 'info');
            }
        };
        
        // 通讯录读取失败回调
        window.onContactsError = function(error) {
            console.error('读取联系人失败:', error);
            showResult('❌ 读取联系人失败: ' + error, 'error');
        };
        
        // 权限被拒绝回调
        window.onContactsPermissionDenied = function() {
            console.log('用户拒绝了通讯录权限');
            showResult('❌ 用户拒绝了通讯录权限', 'error');
            
            const statusDiv = document.getElementById('permission-status');
            statusDiv.className = 'status error';
            statusDiv.textContent = '❌ 通讯录权限被拒绝';
        };
        
        // 页面加载完成后检测环境
        document.addEventListener('DOMContentLoaded', function() {
            detectAppEnvironment();
        });
    </script>
</body>
</html>
