# 代乱设计贷款 Android APP

这是一个基于WebView的Android应用，集成了原生通信录读取功能。

## 📱 功能特性

- ✅ **WebView加载网站**：加载 https://dailuanshej.cn/
- ✅ **原生通信录读取**：支持读取手机通信录
- ✅ **JavaScript接口**：Web页面可调用原生功能
- ✅ **权限管理**：动态请求通信录权限
- ✅ **下拉刷新**：支持下拉刷新网页
- ✅ **返回键处理**：支持网页内返回导航

## 🛠️ 开发环境要求

- **Android Studio**: 2022.3.1 或更高版本
- **JDK**: 8 或更高版本
- **Android SDK**: API 21 (Android 5.0) 或更高
- **Gradle**: 8.1.2

## 📦 项目结构

```
android_app/
├── app/
│   ├── build.gradle                 # 应用级构建配置
│   ├── proguard-rules.pro          # 代码混淆规则
│   └── src/main/
│       ├── AndroidManifest.xml     # 应用清单文件
│       ├── java/com/dailuanshej/loan/
│       │   └── MainActivity.java   # 主活动类
│       └── res/
│           ├── layout/
│           │   └── activity_main.xml    # 主界面布局
│           ├── values/
│           │   ├── colors.xml           # 颜色定义
│           │   ├── strings.xml          # 字符串资源
│           │   └── themes.xml           # 主题样式
├── build.gradle                    # 项目级构建配置
├── gradle.properties              # Gradle属性配置
├── settings.gradle                 # 项目设置
└── README.md                       # 项目说明文档
```

## 🚀 快速开始

### 1. 导入项目

1. 打开 Android Studio
2. 选择 "Open an Existing Project"
3. 选择 `android_app` 文件夹
4. 等待 Gradle 同步完成

### 2. 配置签名（可选）

在 `app/build.gradle` 中添加签名配置：

```gradle
android {
    signingConfigs {
        release {
            storeFile file('your-keystore.jks')
            storePassword 'your-store-password'
            keyAlias 'your-key-alias'
            keyPassword 'your-key-password'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            // ... 其他配置
        }
    }
}
```

### 3. 编译运行

1. 连接Android设备或启动模拟器
2. 点击 "Run" 按钮或按 `Shift + F10`
3. 选择目标设备
4. 等待应用安装并启动

## 📱 权限说明

应用需要以下权限：

- `INTERNET`: 网络访问权限（必需）
- `ACCESS_NETWORK_STATE`: 网络状态检测（必需）
- `READ_CONTACTS`: 读取通信录权限（可选）
- `WRITE_EXTERNAL_STORAGE`: 外部存储写入权限（可选）
- `READ_EXTERNAL_STORAGE`: 外部存储读取权限（可选）

## 🔧 JavaScript接口

应用提供了以下JavaScript接口供网页调用：

### AndroidContacts 对象

```javascript
// 请求读取通信录
AndroidContacts.requestContacts();

// 检查通信录权限状态
AndroidContacts.isContactsPermissionGranted();

// 获取应用信息
AndroidContacts.getAppInfo();
```

### 回调函数

网页需要实现以下回调函数：

```javascript
// 通信录读取成功回调
window.onContactsRead = function(contacts) {
    console.log('读取到联系人:', contacts);
    // contacts 是包含 {name, phone} 对象的数组
};

// 通信录读取失败回调
window.onContactsError = function(error) {
    console.log('读取失败:', error);
};

// 权限被拒绝回调
window.onContactsPermissionDenied = function() {
    console.log('用户拒绝了通信录权限');
};
```

## 🌐 网页适配

为了更好地支持APP，建议在网页中添加以下代码：

```javascript
// 检测是否在APP中
function isInApp() {
    return navigator.userAgent.includes('DaiLuanSheJApp') || 
           (typeof AndroidContacts !== 'undefined');
}

// 根据环境显示不同内容
if (isInApp()) {
    // 显示APP专用功能
    showAppFeatures();
} else {
    // 显示Web版本功能
    showWebFeatures();
}
```

## 📋 测试页面

项目包含一个专门的APP测试页面：
- **URL**: https://dailuanshej.cn/app_login.php
- **功能**: 检测APP环境，测试通信录读取

## 🔨 自定义配置

### 修改网站URL

在 `MainActivity.java` 中修改：

```java
private static final String WEBSITE_URL = "https://your-website.com/";
```

### 修改应用名称

在 `app/src/main/res/values/strings.xml` 中修改：

```xml
<string name="app_name">您的应用名称</string>
```

### 修改应用包名

1. 在 `app/build.gradle` 中修改 `applicationId`
2. 重构Java包名
3. 更新 `AndroidManifest.xml` 中的包名引用

## 🐛 常见问题

### 1. 网络安全配置

如果网站使用HTTP，需要在 `AndroidManifest.xml` 中添加：

```xml
<application
    android:usesCleartextTraffic="true"
    ... >
```

### 2. 通信录权限被拒绝

应用会优雅处理权限拒绝，不影响正常使用。

### 3. WebView兼容性

应用已配置支持现代Web标准，包括JavaScript、DOM存储等。

## 📦 打包发布

### Debug版本

```bash
./gradlew assembleDebug
```

### Release版本

```bash
./gradlew assembleRelease
```

生成的APK文件位于：
- Debug: `app/build/outputs/apk/debug/app-debug.apk`
- Release: `app/build/outputs/apk/release/app-release.apk`

## 📄 许可证

本项目仅供学习和开发使用。

## 🤝 技术支持

如有问题，请联系开发团队。

---

**注意**: 请确保您的网站支持HTTPS，以获得最佳的安全性和兼容性。
